import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import localization files
import enTranslation from './locales/en.json';

// Initialize i18n
i18n
  .use(LanguageDetector) // Automatically detect user browser language
  .use(initReactI18next) // Initialize react-i18next
  .init({
    resources: {
      en: {
        translation: enTranslation
      }
    },
    fallbackLng: 'en', // Use English if language detection fails or translation is not available
    debug: true, // Enable debug mode in development environment for troubleshooting
    interpolation: {
      escapeValue: false // React is already safe from XSS, no need to escape
    },
    detection: {
      // Language detection options
      order: ['querystring', 'cookie', 'localStorage', 'navigator'],
      lookupQuerystring: 'lng', // URL parameter name, e.g.: ?lng=en
      lookupCookie: 'i18next', // Cookie name
      lookupLocalStorage: 'i18nextLng', // localStorage key name
      caches: ['localStorage', 'cookie'], // Cache user language selection
    }
  });

// Output currently detected language for debugging
console.log('i18next initialized with language:', i18n.language);

// 监听语言变化
i18n.on('languageChanged', (lng) => {
  console.log('Language changed to:', lng);
});

export default i18n; 